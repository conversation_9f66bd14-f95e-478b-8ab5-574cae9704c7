package model

type RegResult struct {
	ToSIPX  any `json:"to_sipx"`
	ToRedis any `json:"to_redis"`
}
type RedisResult struct {
	CallID     string    `json:"call_id"`
	DataRecord []*Record `json:"data_record"`
}
type Record struct {
	Timestamp        string `json:"timestamp"`
	Text             string `json:"text"`
	BufferTranscript string `json:"buffer_transcript"`
	Speaker          string `json:"speaker"`
	BegTimeStamp     string `json:"beg_time_stamp"`
	EndTimeStamp     string `json:"end_time_stamp"`
}
